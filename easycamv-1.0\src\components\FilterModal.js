import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  Modal, 
  StyleSheet, 
  Dimensions 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { height } = Dimensions.get('window');

export default function FilterModal({ visible, onClose, currentFilter, onFilterChange }) {
  const filters = [
    { key: 'all', label: 'Todo', icon: 'grid', description: 'Fotos y videos' },
    { key: 'photo', label: 'Fotos', icon: 'image', description: 'Solo imágenes' },
    { key: 'video', label: 'Videos', icon: 'videocam', description: 'Solo videos' },
  ];

  const handleFilterSelect = (filterKey) => {
    onFilterChange(filterKey);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Filtrar por tipo</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          {/* Filtros */}
          <View style={styles.filtersContainer}>
            {filters.map((filter) => (
              <TouchableOpacity
                key={filter.key}
                style={[
                  styles.filterOption,
                  currentFilter === filter.key && styles.selectedFilter
                ]}
                onPress={() => handleFilterSelect(filter.key)}
                activeOpacity={0.7}
              >
                <View style={styles.filterContent}>
                  <View style={[
                    styles.iconContainer,
                    currentFilter === filter.key && styles.selectedIconContainer
                  ]}>
                    <Ionicons 
                      name={filter.icon} 
                      size={24} 
                      color={currentFilter === filter.key ? '#fff' : '#666'} 
                    />
                  </View>
                  
                  <View style={styles.filterText}>
                    <Text style={[
                      styles.filterLabel,
                      currentFilter === filter.key && styles.selectedLabel
                    ]}>
                      {filter.label}
                    </Text>
                    <Text style={styles.filterDescription}>
                      {filter.description}
                    </Text>
                  </View>
                </View>

                {currentFilter === filter.key && (
                  <Ionicons name="checkmark-circle" size={24} color="#007AFF" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    padding: 20,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
  },
  selectedFilter: {
    backgroundColor: '#e3f2fd',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  filterContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  selectedIconContainer: {
    backgroundColor: '#007AFF',
  },
  filterText: {
    flex: 1,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  selectedLabel: {
    color: '#007AFF',
  },
  filterDescription: {
    fontSize: 14,
    color: '#666',
  },
});
