import React, { createContext, useContext, useState } from 'react';

// Crear el contexto
const AppContext = createContext();

// Hook personalizado para usar el contexto
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext debe ser usado dentro de AppProvider');
  }
  return context;
};

// Proveedor del contexto
export const AppProvider = ({ children }) => {
  // Configuraciones globales
  const [settings, setSettings] = useState({
    watermarkEnabled: false,
    watermarkText: 'EasyCam',
    quality: 'high', // 'low', 'medium', 'high'
    flashMode: 'auto', // 'auto', 'on', 'off'
  });

  // Estado de la cámara
  const [cameraState, setCameraState] = useState({
    currentMode: 'photo', // 'photo' o 'video'
    cameraType: 'back', // 'front' o 'back'
    isCapturing: false,
    isRecording: false,
  });

  // Funciones para actualizar configuraciones
  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const updateCameraState = (newState) => {
    setCameraState(prev => ({ ...prev, ...newState }));
  };

  // Valor del contexto
  const value = {
    settings,
    cameraState,
    updateSettings,
    updateCameraState,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};
