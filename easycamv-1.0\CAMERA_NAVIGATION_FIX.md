# 🔧 Solución: Problema de Navegación de Cámara

## 🚨 **Problema Identificado**

Cuando el usuario navega desde CameraScreen → SettingsScreen → CameraScreen, la cámara no se reinicia correctamente, quedando en un estado "congelado" o no responsivo.

## 🔍 **Causa Raíz**

React Navigation no maneja automáticamente el ciclo de vida de componentes nativos como `CameraView`. Cuando la pantalla pierde y recupera el foco, el componente de cámara puede quedar en un estado inconsistente.

## ✅ **Solución Implementada**

### **1. Hook useFocusEffect**
- **Importado**: `useFocusEffect` de `@react-navigation/native`
- **Función**: Detecta cuando la pantalla gana/pierde foco
- **Beneficio**: Control preciso del ciclo de vida de la cámara

### **2. Estado de Control de Cámara**
```javascript
const [isCameraActive, setIsCameraActive] = useState(true);
```
- **Propósito**: Controlar si la cámara debe estar activa
- **Sincronización**: Con el foco de la pantalla

### **3. Gestión del Ciclo de Vida**
```javascript
useFocusEffect(
  React.useCallback(() => {
    // Al ganar foco: activar cámara con delay
    const timer = setTimeout(() => {
      setIsCameraActive(true);
    }, 100);
    
    return () => {
      // Al perder foco: desactivar cámara y limpiar
      clearTimeout(timer);
      setIsCameraActive(false);
      // Detener grabación si está activa
      if (isRecording && cameraRef.current) {
        cameraRef.current.stopRecording();
        setIsRecording(false);
      }
    };
  }, [isRecording])
);
```

### **4. Prop Active en CameraView**
- **CameraPreview actualizado**: Recibe prop `active`
- **CameraView**: Usa prop `active={isCameraActive}`
- **Beneficio**: Control nativo del estado de la cámara

### **5. Pantalla de Carga**
```javascript
if (!isCameraActive) {
  return (
    <View style={styles.center}>
      <Text style={{ color: '#fff', fontSize: 16 }}>Iniciando cámara...</Text>
    </View>
  );
}
```
- **UX mejorada**: Feedback visual durante reinicio
- **Prevención**: Evita pantalla negra o congelada

## 🎯 **Beneficios de la Solución**

### **✅ Funcionalidad**
- **Reinicio automático**: Cámara se reinicia al regresar de configuraciones
- **Limpieza de estado**: Detiene grabaciones activas al navegar
- **Prevención de errores**: Evita estados inconsistentes

### **✅ Experiencia de Usuario**
- **Transición suave**: Delay de 100ms para navegación fluida
- **Feedback visual**: Mensaje "Iniciando cámara..." durante reinicio
- **Consistencia**: Comportamiento predecible en navegación

### **✅ Rendimiento**
- **Gestión de recursos**: Desactiva cámara cuando no está en uso
- **Limpieza automática**: Libera recursos al cambiar de pantalla
- **Optimización**: Evita procesos innecesarios en background

## 🧪 **Cómo Probar la Solución**

### **Escenario 1: Navegación Básica**
1. Abrir CameraScreen
2. Tocar icono de configuraciones
3. Ir a SettingsScreen
4. Regresar con botón "Volver a Cámara"
5. **Resultado esperado**: Cámara se reinicia correctamente

### **Escenario 2: Durante Grabación**
1. Iniciar grabación de video
2. Navegar a configuraciones
3. **Resultado esperado**: Grabación se detiene automáticamente
4. Regresar a cámara
5. **Resultado esperado**: Cámara lista para nueva grabación

### **Escenario 3: Cambios de Configuración**
1. Cambiar configuración de flash en SettingsScreen
2. Regresar a CameraScreen
3. **Resultado esperado**: Nueva configuración aplicada correctamente

## 📱 **Compatibilidad**

- **✅ iOS**: Funciona con prop `active` nativo
- **✅ Android**: Compatible con gestión de ciclo de vida
- **✅ Expo Go**: Funcional en entorno de desarrollo
- **✅ Build nativo**: Optimizado para producción

## 🔄 **Flujo de Estados**

```
CameraScreen Focus → isCameraActive: true → CameraView activa
       ↓
Navigation to Settings → isCameraActive: false → CameraView inactiva
       ↓
Return to Camera → Timer 100ms → isCameraActive: true → CameraView reiniciada
```

## 🎉 **Estado Actual**

**✅ PROBLEMA RESUELTO**

La navegación entre CameraScreen y SettingsScreen ahora funciona correctamente, con reinicio automático de la cámara y gestión adecuada del estado.
