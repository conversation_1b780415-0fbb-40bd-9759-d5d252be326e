import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  StatusBar,
  TouchableOpacity,
  Text,
  Alert,
  PanResponder,
} from 'react-native';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { Video } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import * as MediaLibrary from 'expo-media-library';
import ShareModal from '../components/ShareModal';

const { width, height } = Dimensions.get('window');

export default function MediaViewerScreen({ route, navigation }) {
  const { assets, initialIndex = 0 } = route.params;
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [showControls, setShowControls] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const videoRef = useRef(null);

  // Valores animados para zoom
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const currentAsset = assets[currentIndex];
  const isVideo = currentAsset?.mediaType === 'video';

  useEffect(() => {
    // Ocultar controles automáticamente después de 3 segundos
    const timer = setTimeout(() => {
      setShowControls(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [currentIndex, showControls]);

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const handlePinchGesture = (event) => {
    scale.value = event.nativeEvent.scale;
  };

  const handlePinchEnd = () => {
    if (scale.value < 1) {
      scale.value = withSpring(1);
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    } else if (scale.value > 3) {
      scale.value = withSpring(3);
    }
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  const navigateToAsset = (direction) => {
    const newIndex = direction === 'next' 
      ? Math.min(currentIndex + 1, assets.length - 1)
      : Math.max(currentIndex - 1, 0);
    
    if (newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
      // Reset zoom
      scale.value = withSpring(1);
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
      setIsPlaying(false);
    }
  };

  const handleShare = () => {
    setShowShareModal(true);
  };

  const handleDelete = () => {
    Alert.alert(
      'Eliminar',
      `¿Estás seguro de que quieres eliminar este ${isVideo ? 'video' : 'foto'}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            try {
              await MediaLibrary.deleteAssetsAsync([currentAsset]);
              // Remover del array local
              const updatedAssets = assets.filter((_, index) => index !== currentIndex);
              
              if (updatedAssets.length === 0) {
                navigation.goBack();
              } else {
                // Ajustar índice si es necesario
                const newIndex = currentIndex >= updatedAssets.length 
                  ? updatedAssets.length - 1 
                  : currentIndex;
                setCurrentIndex(newIndex);
              }
            } catch (error) {
              Alert.alert('Error', 'No se pudo eliminar el archivo');
            }
          },
        },
      ]
    );
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      <TouchableOpacity 
        style={styles.mediaContainer} 
        activeOpacity={1}
        onPress={toggleControls}
      >
        <PinchGestureHandler
          onGestureEvent={handlePinchGesture}
          onHandlerStateChange={(event) => {
            if (event.nativeEvent.state === State.END) {
              handlePinchEnd();
            }
          }}
        >
          <Animated.View style={[styles.mediaWrapper, animatedStyle]}>
            {isVideo ? (
              <Video
                ref={videoRef}
                source={{ uri: currentAsset.uri }}
                style={styles.media}
                useNativeControls
                resizeMode="contain"
                isLooping
                onPlaybackStatusUpdate={(status) => {
                  setIsPlaying(status.isPlaying);
                }}
              />
            ) : (
              <Animated.Image
                source={{ uri: currentAsset.uri }}
                style={styles.media}
                resizeMode="contain"
              />
            )}
          </Animated.View>
        </PinchGestureHandler>
      </TouchableOpacity>

      {/* Controles superiores */}
      {showControls && (
        <Animated.View style={styles.topControls}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          
          <View style={styles.infoContainer}>
            <Text style={styles.indexText}>
              {currentIndex + 1} de {assets.length}
            </Text>
            <Text style={styles.dateText}>
              {formatDate(currentAsset.creationTime)}
            </Text>
          </View>

          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Ionicons name="share-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Controles inferiores */}
      {showControls && (
        <Animated.View style={styles.bottomControls}>
          <TouchableOpacity 
            style={[styles.navButton, currentIndex === 0 && styles.disabledButton]}
            onPress={() => navigateToAsset('prev')}
            disabled={currentIndex === 0}
          >
            <Ionicons 
              name="chevron-back" 
              size={24} 
              color={currentIndex === 0 ? '#666' : '#fff'} 
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <Ionicons name="trash-outline" size={24} color="#ff4444" />
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.navButton, currentIndex === assets.length - 1 && styles.disabledButton]}
            onPress={() => navigateToAsset('next')}
            disabled={currentIndex === assets.length - 1}
          >
            <Ionicons 
              name="chevron-forward" 
              size={24} 
              color={currentIndex === assets.length - 1 ? '#666' : '#fff'} 
            />
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Modal de compartir */}
      <ShareModal
        visible={showShareModal}
        onClose={() => setShowShareModal(false)}
        asset={currentAsset}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  mediaContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaWrapper: {
    width: width,
    height: height,
    justifyContent: 'center',
    alignItems: 'center',
  },
  media: {
    width: width,
    height: height * 0.8,
  },
  topControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: StatusBar.currentHeight || 44,
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  indexText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  dateText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginTop: 2,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
    paddingVertical: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  navButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  deleteButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 68, 68, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
