import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, Alert, Text } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import * as MediaLibrary from 'expo-media-library';
import GalleryHeader from '../components/GalleryHeader';
import MediaGrid from '../components/MediaGrid';
import FilterModal from '../components/FilterModal';

export default function GalleryScreen({ navigation }) {
  const [assets, setAssets] = useState([]);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [endCursor, setEndCursor] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentFilter, setCurrentFilter] = useState('all');
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [permission, requestPermission] = MediaLibrary.usePermissions();

  // Configuración de consulta
  const ASSETS_PER_PAGE = 50;

  // Solicitar permisos al cargar la pantalla
  useFocusEffect(
    useCallback(() => {
      if (permission?.status !== 'granted') {
        requestPermission();
      } else {
        loadAssets(true); // Cargar desde el inicio
      }
    }, [permission?.status, currentFilter])
  );

  const loadAssets = async (reset = false) => {
    if (loading) return;
    if (!reset && !hasNextPage) return;

    setLoading(true);

    try {
      const options = {
        first: ASSETS_PER_PAGE,
        mediaType: getMediaType(),
        sortBy: [MediaLibrary.SortBy.creationTime],
      };

      // Si no es reset, usar cursor para paginación
      if (!reset && endCursor) {
        options.after = endCursor;
      }

      const result = await MediaLibrary.getAssetsAsync(options);

      if (reset) {
        setAssets(result.assets);
      } else {
        setAssets(prev => [...prev, ...result.assets]);
      }

      setHasNextPage(result.hasNextPage);
      setEndCursor(result.endCursor);
    } catch (error) {
      console.error('Error loading assets:', error);
      Alert.alert(
        'Error',
        'No se pudieron cargar las fotos y videos. Verifica los permisos.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const getMediaType = () => {
    switch (currentFilter) {
      case 'photo':
        return MediaLibrary.MediaType.photo;
      case 'video':
        return MediaLibrary.MediaType.video;
      default:
        return MediaLibrary.MediaType.all;
    }
  };

  const handleAssetPress = (asset, index) => {
    // Navegar al visualizador de medios
    navigation.navigate('MediaViewer', {
      assets,
      initialIndex: index,
      currentFilter
    });
  };

  const handleFilterChange = (newFilter) => {
    setCurrentFilter(newFilter);
    // Reset y recargar con nuevo filtro
    setAssets([]);
    setEndCursor(null);
    setHasNextPage(true);
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  // Mostrar pantalla de permisos si no están concedidos
  if (permission?.status !== 'granted') {
    return (
      <View style={styles.permissionContainer}>
        <GalleryHeader 
          title="Galería" 
          onBackPress={handleBackPress}
          assetCount={0}
        />
        <View style={styles.permissionContent}>
          <Text style={styles.permissionTitle}>
            Permisos de Galería Requeridos
          </Text>
          <Text style={styles.permissionText}>
            Para mostrar tus fotos y videos, necesitamos acceso a tu galería.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <GalleryHeader
        title="Galería"
        onBackPress={handleBackPress}
        onFilterPress={() => setShowFilterModal(true)}
        currentFilter={currentFilter}
        assetCount={assets.length}
      />

      <MediaGrid
        assets={assets}
        onAssetPress={handleAssetPress}
        onLoadMore={() => loadAssets(false)}
        hasNextPage={hasNextPage}
        loading={loading}
      />

      <FilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        currentFilter={currentFilter}
        onFilterChange={handleFilterChange}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  permissionContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  permissionContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
  },
  permissionText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
