import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function FlashControl({ flashMode, onFlashModeChange }) {
  const getFlashIcon = () => {
    switch (flashMode) {
      case 'on':
        return 'flash';
      case 'off':
        return 'flash-off';
      case 'auto':
      default:
        return 'flash-outline';
    }
  };

  const getFlashColor = () => {
    switch (flashMode) {
      case 'on':
        return '#FFD700'; // Dorado para indicar que está encendido
      case 'off':
        return '#FF3B30'; // Rojo para indicar que está apagado
      case 'auto':
      default:
        return '#fff'; // Blanco para modo automático
    }
  };

  const cycleFlashMode = () => {
    const modes = ['auto', 'on', 'off'];
    const currentIndex = modes.indexOf(flashMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    onFlashModeChange(modes[nextIndex]);
  };

  return (
    <TouchableOpacity
      style={styles.flashButton}
      onPress={cycleFlashMode}
      activeOpacity={0.7}
    >
      <Ionicons 
        name={getFlashIcon()} 
        size={24} 
        color={getFlashColor()} 
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  flashButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10, // Espaciado entre controles
  },
});
