import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  Alert,
  Share,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Sharing from 'expo-sharing';
import * as MediaLibrary from 'expo-media-library';

const { height } = Dimensions.get('window');

export default function ShareModal({ visible, onClose, asset }) {
  if (!asset) return null;

  const isVideo = asset.mediaType === 'video';

  const shareOptions = [
    {
      key: 'native',
      label: 'Compartir',
      icon: 'share-outline',
      description: 'Usar el menú nativo de compartir',
      action: handleNativeShare,
    },
    {
      key: 'save',
      label: 'Guardar en Galería',
      icon: 'download-outline',
      description: 'Guardar una copia en la galería',
      action: handleSaveToGallery,
    },
    {
      key: 'info',
      label: 'Información',
      icon: 'information-circle-outline',
      description: 'Ver detalles del archivo',
      action: handleShowInfo,
    },
  ];

  async function handleNativeShare() {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(asset.uri, {
          mimeType: isVideo ? 'video/mp4' : 'image/jpeg',
          dialogTitle: `Compartir ${isVideo ? 'video' : 'foto'}`,
        });
      } else {
        // Fallback para plataformas que no soportan expo-sharing
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          await Share.share({
            url: asset.uri,
            title: `Compartir ${isVideo ? 'video' : 'foto'}`,
          });
        } else {
          Alert.alert('Error', 'La función de compartir no está disponible');
        }
      }
      onClose();
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert('Error', 'No se pudo compartir el archivo');
    }
  }

  async function handleSaveToGallery() {
    try {
      // Crear un álbum personalizado para EasyCam si no existe
      const album = await MediaLibrary.getAlbumAsync('EasyCam');
      
      if (album) {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      } else {
        await MediaLibrary.createAlbumAsync('EasyCam', asset, false);
      }
      
      Alert.alert(
        'Guardado',
        `${isVideo ? 'Video' : 'Foto'} guardado en el álbum EasyCam`,
        [{ text: 'OK' }]
      );
      onClose();
    } catch (error) {
      console.error('Error saving to gallery:', error);
      Alert.alert('Error', 'No se pudo guardar en la galería');
    }
  }

  async function handleShowInfo() {
    try {
      const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
      
      const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      const formatDate = (timestamp) => {
        return new Date(timestamp).toLocaleString('es-ES', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      };

      const info = [
        `Tipo: ${isVideo ? 'Video' : 'Foto'}`,
        `Tamaño: ${formatFileSize(assetInfo.fileSize || 0)}`,
        `Dimensiones: ${asset.width} × ${asset.height}`,
        `Fecha: ${formatDate(asset.creationTime)}`,
      ];

      if (isVideo && asset.duration) {
        const duration = Math.round(asset.duration);
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        info.push(`Duración: ${minutes}:${seconds.toString().padStart(2, '0')}`);
      }

      if (assetInfo.location) {
        info.push(`Ubicación: ${assetInfo.location.latitude.toFixed(6)}, ${assetInfo.location.longitude.toFixed(6)}`);
      }

      Alert.alert(
        'Información del archivo',
        info.join('\n'),
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error getting asset info:', error);
      Alert.alert('Error', 'No se pudo obtener la información del archivo');
    }
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>
              Opciones de {isVideo ? 'video' : 'foto'}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          {/* Opciones */}
          <View style={styles.optionsContainer}>
            {shareOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={styles.option}
                onPress={option.action}
                activeOpacity={0.7}
              >
                <View style={styles.optionContent}>
                  <View style={styles.iconContainer}>
                    <Ionicons name={option.icon} size={24} color="#007AFF" />
                  </View>
                  
                  <View style={styles.optionText}>
                    <Text style={styles.optionLabel}>{option.label}</Text>
                    <Text style={styles.optionDescription}>
                      {option.description}
                    </Text>
                  </View>
                </View>

                <Ionicons name="chevron-forward" size={20} color="#ccc" />
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.6,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionsContainer: {
    padding: 20,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#e3f2fd',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionText: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
  },
});
