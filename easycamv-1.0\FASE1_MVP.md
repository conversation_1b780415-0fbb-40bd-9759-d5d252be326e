# Fase 1: MVP - Funcionalidades Básicas y Estructura

## Objetivo
Tener una app funcional con captura manual de fotos y videos, navegación básica y estructura de estado inicial.

---

## Plan paso a paso

### 1. Instalación de dependencias esenciales
- Instalar react-navigation y sus dependencias.
- Instalar expo-camera y expo-media-library.
- Probar que la app arranca correctamente.

### 2. Configuración de la navegación
- Crear el stack de navegación entre CameraScreen y SettingsScreen.
- Verificar que la navegación funciona.

### 3. Implementación de CameraScreen básica
- Mostrar un mensaje o placeholder.
- Probar que la pantalla carga correctamente.

### 4. Integrar expo-camera en CameraScreen
- Solicitar permisos de cámara.
- Mostrar la previsualización de la cámara.
- Probar en dispositivo real o emulador.

### 5. Agregar botones para tomar foto y grabar/detener video
- Implementar la lógica básica de captura.
- Probar que se pueden tomar fotos y grabar videos.

### 6. Guardar fotos/videos en la galería (expo-media-library)
- Solicitar permisos de almacenamiento.
- Guardar archivos capturados.
- Verificar que aparecen en la galería del dispositivo.

### 7. Crear SettingsScreen básica
- Mostrar un mensaje o placeholder.
- Probar la navegación desde CameraScreen.

### 8. Implementar cambio de modo Foto/Video
- Agregar un switch o botón para alternar entre modos.
- Probar que la UI y la lógica cambian correctamente.

### 9. Manejo de estado inicial (Context API)
- Crear un contexto simple para configuraciones básicas (ejemplo: modo actual).
- Probar que el estado se comparte entre pantallas.

---

> Avanza paso a paso, validando el funcionamiento en cada etapa antes de continuar. Así aseguras una base sólida y fácil de mantener.
