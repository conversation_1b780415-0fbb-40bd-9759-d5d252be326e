{"version": 3, "file": "Sharing.js", "sourceRoot": "", "sources": ["../src/Sharing.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,OAAO,MAAM,eAAe,CAAC;AAiCpC,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC1C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,GAAW,EAAE,UAA0B,EAAE;IACxE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QACpC,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,MAAM,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport Sharing from './ExpoSharing';\n\n// @needsAudit\nexport type SharingOptions = {\n  /**\n   * Sets `mimeType` for `Intent`.\n   * @platform android\n   */\n  mimeType?: string;\n  /**\n   * [Uniform Type Identifier](https://developer.apple.com/library/archive/documentation/FileManagement/Conceptual/understanding_utis/understand_utis_conc/understand_utis_conc.html)\n   *  - the type of the target file.\n   * @platform ios\n   */\n  UTI?: string;\n  /**\n   * Sets share dialog title.\n   * @platform android\n   * @platform web\n   */\n  dialogTitle?: string;\n  /**\n   * set the anchor point for iPad\n   * @platform ios\n   */\n  anchor?: {\n    x?: number;\n    y?: number;\n    width?: number;\n    height?: number;\n  };\n};\n\n// @needsAudit\n/**\n * Determine if the sharing API can be used in this app.\n * @return A promise that fulfills with `true` if the sharing API can be used, and `false` otherwise.\n */\nexport async function isAvailableAsync(): Promise<boolean> {\n  if (Sharing) {\n    if (Sharing.isAvailableAsync) {\n      return await Sharing.isAvailableAsync();\n    }\n    return true;\n  }\n\n  return false;\n}\n\n// @needsAudit\n/**\n * Opens action sheet to share file to different applications which can handle this type of file.\n * @param url Local file URL to share.\n * @param options A map of share options.\n */\nexport async function shareAsync(url: string, options: SharingOptions = {}): Promise<void> {\n  if (!Sharing || !Sharing.shareAsync) {\n    throw new UnavailabilityError('Sharing', 'shareAsync');\n  }\n  return await Sharing.shareAsync(url, options);\n}\n"]}