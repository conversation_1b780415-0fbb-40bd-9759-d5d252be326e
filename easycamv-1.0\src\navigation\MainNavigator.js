import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import CameraScreen from '../screens/CameraScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Stack = createStackNavigator();

export default function MainNavigator() {
  return (
    <Stack.Navigator initialRouteName="CameraScreen">
      <Stack.Screen name="CameraScreen" component={CameraScreen} options={{ title: '<PERSON><PERSON><PERSON>' }} />
      <Stack.Screen name="SettingsScreen" component={SettingsScreen} options={{ title: 'Configuración' }} />
    </Stack.Navigator>
  );
}
