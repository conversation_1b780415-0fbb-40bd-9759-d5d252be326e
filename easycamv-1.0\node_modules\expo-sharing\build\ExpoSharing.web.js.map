{"version": 3, "file": "ExpoSharing.web.js", "sourceRoot": "", "sources": ["../src/ExpoSharing.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe;IACb,KAAK,CAAC,gBAAgB;QACpB,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC;IAC3B,CAAC;IACD,KAAK,CAAC,UAAU,CAAC,GAAW,EAAE,UAAwB,EAAE;QACtD,sDAAsD;QACtD,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,MAAM,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;CACF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\ntype ShareOptions = { title?: string; text?: string; url?: string };\n\nexport default {\n  async isAvailableAsync(): Promise<boolean> {\n    if (typeof navigator === 'undefined') {\n      return false;\n    }\n\n    return !!navigator.share;\n  },\n  async shareAsync(url: string, options: ShareOptions = {}): Promise<void> {\n    // NOTE: `navigator.share` is only available via HTTPS\n    if (navigator.share) {\n      await navigator.share({ ...options, url });\n    } else {\n      throw new UnavailabilityError('navigator', 'share');\n    }\n  },\n};\n"]}