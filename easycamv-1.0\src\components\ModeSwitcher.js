import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';

export default function ModeSwitcher({ mode, onModeChange }) {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, mode === 'photo' && styles.activeButton]}
        onPress={() => onModeChange('photo')}
      >
        <Text style={[styles.text, mode === 'photo' && styles.activeText]}>
          Foto
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.button, mode === 'video' && styles.activeButton]}
        onPress={() => onModeChange('video')}
      >
        <Text style={[styles.text, mode === 'video' && styles.activeText]}>
          Video
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 4,
    marginBottom: 10,
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 16,
    minWidth: 60,
    alignItems: 'center',
  },
  activeButton: {
    backgroundColor: '#fff',
  },
  text: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  activeText: {
    color: '#000',
  },
});
