import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';

export default function ModeSwitcher({ mode, onModeChange }) {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, mode === 'photo' && styles.activeButton]}
        onPress={() => onModeChange('photo')}
      >
        <Text style={[styles.text, mode === 'photo' && styles.activeText]}>
          FOTO
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, mode === 'video' && styles.activeButton]}
        onPress={() => onModeChange('video')}
      >
        <Text style={[styles.text, mode === 'video' && styles.activeText]}>
          VIDEO
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 25,
    padding: 3,
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 22,
    minWidth: 80,
    alignItems: 'center',
  },
  activeButton: {
    backgroundColor: '#FFD700',
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  text: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  activeText: {
    color: '#000',
  },
});
