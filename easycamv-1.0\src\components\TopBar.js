import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function TopBar({ onSettingsPress, onGalleryPress }) {
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.iconButton} onPress={onGalleryPress}>
        <Ionicons name="images-outline" size={28} color="#fff" />
      </TouchableOpacity>
      
      <View style={styles.spacer} />
      
      <TouchableOpacity style={styles.iconButton} onPress={onSettingsPress}>
        <Ionicons name="settings-outline" size={28} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 40, // Alineado con BottomControls para uniformidad vertical
    paddingTop: 20, // Alineado con centerControls para consistencia
    paddingBottom: 10, // Reducido para mejor alineación
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  spacer: {
    flex: 1,
  },
});
