import React from 'react';
import { View, StyleSheet } from 'react-native';
import FlashControl from './FlashControl';

export default function SideControls({ flashMode, onFlashModeChange }) {
  return (
    <View style={styles.container}>
      <FlashControl 
        flashMode={flashMode} 
        onFlashModeChange={onFlashModeChange} 
      />
      {/* Aquí se pueden agregar más controles laterales en el futuro */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: 20,
    top: '50%',
    transform: [{ translateY: -50 }], // Centrar verticalmente
    zIndex: 15, // Entre topBar y centerControls
    alignItems: 'center',
  },
});
