
import React from 'react';
import {
  View,
  Text,
  Button,
  StyleSheet,
  ScrollView,
  Switch,
  TextInput,
  TouchableOpacity
} from 'react-native';
import { useAppContext } from '../contexts/AppContext';

export default function SettingsScreen({ navigation }) {
  const { settings, updateSettings } = useAppContext();

  const qualityOptions = [
    { label: 'Baja', value: 'low' },
    { label: 'Media', value: 'medium' },
    { label: 'Alta', value: 'high' },
  ];

  const flashOptions = [
    { label: 'Automático', value: 'auto' },
    { label: 'Encendido', value: 'on' },
    { label: 'Apagado', value: 'off' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Marca de Agua</Text>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Activar marca de agua</Text>
          <Switch
            value={settings.watermarkEnabled}
            onValueChange={(value) => updateSettings({ watermarkEnabled: value })}
          />
        </View>

        {settings.watermarkEnabled && (
          <View style={styles.settingRow}>
            <Text style={styles.settingLabel}>Texto de marca de agua</Text>
            <TextInput
              style={styles.textInput}
              value={settings.watermarkText}
              onChangeText={(text) => updateSettings({ watermarkText: text })}
              placeholder="Ingresa el texto"
            />
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Calidad</Text>
        {qualityOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.qualityOption,
              settings.quality === option.value && styles.selectedOption
            ]}
            onPress={() => updateSettings({ quality: option.value })}
          >
            <Text style={[
              styles.qualityText,
              settings.quality === option.value && styles.selectedText
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Flash</Text>
        {flashOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.qualityOption,
              settings.flashMode === option.value && styles.selectedOption
            ]}
            onPress={() => updateSettings({ flashMode: option.value })}
          >
            <Text style={[
              styles.qualityText,
              settings.flashMode === option.value && styles.selectedText
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Ayuda</Text>
        <Text style={styles.helpText}>
          Comandos de voz disponibles (próximamente):
        </Text>
        <Text style={styles.commandText}>• "Tomar foto"</Text>
        <Text style={styles.commandText}>• "Grabar video"</Text>
        <Text style={styles.commandText}>• "Detener grabación"</Text>
        <Text style={styles.commandText}>• "Cambiar cámara"</Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title="Volver a Cámara"
          onPress={() => navigation.navigate('CameraScreen')}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 15,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 8,
    flex: 1,
    marginLeft: 10,
  },
  qualityOption: {
    padding: 12,
    borderRadius: 5,
    marginBottom: 5,
    backgroundColor: '#f0f0f0',
  },
  selectedOption: {
    backgroundColor: '#007AFF',
  },
  qualityText: {
    fontSize: 16,
    color: '#333',
  },
  selectedText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  helpText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  commandText: {
    fontSize: 14,
    color: '#888',
    marginLeft: 10,
    marginBottom: 5,
  },
  buttonContainer: {
    margin: 20,
  },
});
