
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Camera } from 'expo-camera';

export default function CameraPreview({ cameraRef, type }) {
  return (
    <View style={styles.container}>
      <Camera
        style={styles.camera}
        type={type}
        ref={cameraRef}
        ratio="16:9"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  camera: {
    flex: 1,
    width: '100%',
  },
});
