
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { CameraView } from 'expo-camera';

export default function CameraPreview({ cameraRef, type, mode = 'photo' }) {
  return (
    <View style={styles.container}>
      <CameraView
        style={styles.camera}
        facing={type}
        ref={cameraRef}
        mode={mode === 'photo' ? 'picture' : 'video'}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  camera: {
    flex: 1,
    width: '100%',
  },
});
