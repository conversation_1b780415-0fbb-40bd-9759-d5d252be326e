
import React, { useState, useRef, useEffect } from 'react';
import { View, Text, Button, StyleSheet, Alert, StatusBar } from 'react-native';
import { useCameraPermissions } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import CameraPreview from '../components/CameraPreview';
import ModeSwitcher from '../components/ModeSwitcher';
import TopBar from '../components/TopBar';
import BottomControls from '../components/BottomControls';

export default function CameraScreen({ navigation }) {
  const [permission, requestPermission] = useCameraPermissions();
  const [hasMediaLibraryPermission, setHasMediaLibraryPermission] = useState(null);
  const [type, setType] = useState('back'); // 'front' o 'back'
  const [mode, setMode] = useState('photo'); // 'photo' o 'video'
  const [isCapturing, setIsCapturing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isMicEnabled, setIsMicEnabled] = useState(true);
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      const mediaLibraryStatus = await MediaLibrary.requestPermissionsAsync();
      setHasMediaLibraryPermission(mediaLibraryStatus.status === 'granted');
    })();
  }, []);

  const takePicture = async () => {
    if (cameraRef.current && permission?.granted && hasMediaLibraryPermission) {
      try {
        setIsCapturing(true);
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: false,
        });

        // Guardar en la galería (limitado en Expo Go)
        try {
          await MediaLibrary.saveToLibraryAsync(photo.uri);
          Alert.alert('¡Éxito!', 'Foto guardada en la galería');
        } catch (mediaError) {
          console.log('Media Library error:', mediaError);
          Alert.alert('Foto tomada', 'Foto capturada correctamente\n(Guardado limitado en Expo Go)');
        }
      } catch (error) {
        console.error('Error al tomar foto:', error);
        Alert.alert('Error', 'No se pudo tomar la foto');
      } finally {
        setIsCapturing(false);
      }
    } else {
      Alert.alert('Error', 'Permisos de cámara o galería no concedidos');
    }
  };

  const startRecording = async () => {
    if (cameraRef.current && permission?.granted && hasMediaLibraryPermission) {
      try {
        setIsRecording(true);
        const video = await cameraRef.current.recordAsync({
          videoQuality: '720p',
          mute: !isMicEnabled,
        });

        // Guardar en la galería (limitado en Expo Go)
        try {
          await MediaLibrary.saveToLibraryAsync(video.uri);
          Alert.alert('¡Éxito!', 'Video guardado en la galería');
        } catch (mediaError) {
          console.log('Media Library error:', mediaError);
          Alert.alert('Video grabado', 'Video capturado correctamente\n(Guardado limitado en Expo Go)');
        }
      } catch (error) {
        console.error('Error al grabar video:', error);
        Alert.alert('Error', 'No se pudo grabar el video');
      } finally {
        setIsRecording(false);
      }
    } else {
      Alert.alert('Error', 'Permisos de cámara o galería no concedidos');
    }
  };

  const stopRecording = () => {
    if (cameraRef.current && isRecording) {
      cameraRef.current.stopRecording();
    }
  };

  const handleCapture = () => {
    if (mode === 'photo') {
      takePicture();
    } else {
      if (isRecording) {
        stopRecording();
      } else {
        startRecording();
      }
    }
  };

  const handleFlipCamera = () => {
    setType(type === 'back' ? 'front' : 'back');
  };

  const handleMicToggle = () => {
    setIsMicEnabled(!isMicEnabled);
  };

  const handleSettingsPress = () => {
    navigation.navigate('SettingsScreen');
  };

  const handleGalleryPress = () => {
    // Función de galería mejorada con más opciones
    Alert.alert(
      'Galería',
      'Función de galería próximamente\n\n• Ver fotos tomadas\n• Ver videos grabados\n• Compartir contenido',
      [
        { text: 'Entendido', style: 'default' }
      ]
    );
  };

  if (!permission) {
    return <View style={styles.center}><Text>Solicitando permisos...</Text></View>;
  }

  if (!permission.granted) {
    return (
      <View style={styles.center}>
        <Text style={{ marginBottom: 20 }}>Necesitamos acceso a la cámara</Text>
        <Button onPress={requestPermission} title="Conceder permiso" />
      </View>
    );
  }

  if (hasMediaLibraryPermission === null) {
    return <View style={styles.center}><Text>Solicitando permisos de galería...</Text></View>;
  }

  if (hasMediaLibraryPermission === false) {
    return <View style={styles.center}><Text>Permiso de galería denegado</Text></View>;
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <CameraPreview cameraRef={cameraRef} type={type} mode={mode} />

      {/* Barra superior */}
      <View style={styles.topBar}>
        <TopBar
          onSettingsPress={handleSettingsPress}
          onGalleryPress={handleGalleryPress}
        />
      </View>

      {/* Controles centrales */}
      <View style={styles.centerControls}>
        <ModeSwitcher mode={mode} onModeChange={setMode} />
      </View>

      {/* Controles inferiores */}
      <View style={styles.bottomControls}>
        <BottomControls
          onCapturePress={handleCapture}
          onFlipCamera={handleFlipCamera}
          onMicToggle={handleMicToggle}
          isRecording={isCapturing || isRecording}
          mode={mode}
          isMicEnabled={isMicEnabled}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  topBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20, // Aumentado para estar por encima de centerControls
    paddingTop: 50, // Para el status bar
    paddingBottom: 15, // Espacio adicional para que las sombras se rendericen correctamente
    overflow: 'visible', // Permite que las sombras se muestren fuera del contenedor
  },
  centerControls: {
    position: 'absolute',
    top: 50, // Alineado con el paddingTop del topBar
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10, // Mantiene el valor original
    paddingTop: 20, // Mismo padding que el TopBar para alineación vertical
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
});
