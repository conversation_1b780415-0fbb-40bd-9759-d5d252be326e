
import React, { useState, useRef, useEffect } from 'react';
import { View, Text, Button, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Camera } from 'expo-camera';
import CameraPreview from '../components/CameraPreview';
import CaptureButton from '../components/CaptureButton';

export default function CameraScreen({ navigation }) {
  const [hasPermission, setHasPermission] = useState(null);
  const [type, setType] = useState(Camera.Constants.Type.back);
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  if (hasPermission === null) {
    return <View style={styles.center}><Text>Solicitando permisos de cámara...</Text></View>;
  }
  if (hasPermission === false) {
    return <View style={styles.center}><Text>Permiso de cámara denegado</Text></View>;
  }

  return (
    <View style={{ flex: 1 }}>
      <CameraPreview cameraRef={cameraRef} type={type} />
      <View style={styles.controls}>
        <CaptureButton onPress={() => Alert.alert('Captura no implementada aún')} />
        <TouchableOpacity onPress={() => setType(
          type === Camera.Constants.Type.back
            ? Camera.Constants.Type.front
            : Camera.Constants.Type.back
        )} style={styles.switchButton}>
          <Text style={{ color: '#fff' }}>Cambiar cámara</Text>
        </TouchableOpacity>
        <Button title="Ir a Configuración" onPress={() => navigation.navigate('SettingsScreen')} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  controls: {
    position: 'absolute',
    bottom: 30,
    width: '100%',
    alignItems: 'center',
  },
  switchButton: {
    marginVertical: 10,
    padding: 10,
    backgroundColor: '#222',
    borderRadius: 8,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
