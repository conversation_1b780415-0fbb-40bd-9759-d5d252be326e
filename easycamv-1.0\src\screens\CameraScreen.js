
import React, { useState, useRef, useEffect } from 'react';
import { View, Text, Button, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Camera, CameraType } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import CameraPreview from '../components/CameraPreview';
import CaptureButton from '../components/CaptureButton';
import ModeSwitcher from '../components/ModeSwitcher';

export default function CameraScreen({ navigation }) {
  const [hasPermission, setHasPermission] = useState(null);
  const [hasMediaLibraryPermission, setHasMediaLibraryPermission] = useState(null);
  const [type, setType] = useState(CameraType.back);
  const [mode, setMode] = useState('photo'); // 'photo' o 'video'
  const [isCapturing, setIsCapturing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      const cameraStatus = await Camera.requestCameraPermissionsAsync();
      const mediaLibraryStatus = await MediaLibrary.requestPermissionsAsync();

      setHasPermission(cameraStatus.status === 'granted');
      setHasMediaLibraryPermission(mediaLibraryStatus.status === 'granted');
    })();
  }, []);

  const takePicture = async () => {
    if (cameraRef.current && hasPermission && hasMediaLibraryPermission) {
      try {
        setIsCapturing(true);
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: false,
        });

        // Guardar en la galería
        await MediaLibrary.saveToLibraryAsync(photo.uri);
        Alert.alert('¡Éxito!', 'Foto guardada en la galería');
      } catch (error) {
        console.error('Error al tomar foto:', error);
        Alert.alert('Error', 'No se pudo tomar la foto');
      } finally {
        setIsCapturing(false);
      }
    } else {
      Alert.alert('Error', 'Permisos de cámara o galería no concedidos');
    }
  };

  const startRecording = async () => {
    if (cameraRef.current && hasPermission && hasMediaLibraryPermission) {
      try {
        setIsRecording(true);
        const video = await cameraRef.current.recordAsync({
          quality: Camera.Constants.VideoQuality['720p'],
        });

        // Guardar en la galería
        await MediaLibrary.saveToLibraryAsync(video.uri);
        Alert.alert('¡Éxito!', 'Video guardado en la galería');
      } catch (error) {
        console.error('Error al grabar video:', error);
        Alert.alert('Error', 'No se pudo grabar el video');
      } finally {
        setIsRecording(false);
      }
    } else {
      Alert.alert('Error', 'Permisos de cámara o galería no concedidos');
    }
  };

  const stopRecording = () => {
    if (cameraRef.current && isRecording) {
      cameraRef.current.stopRecording();
    }
  };

  const handleCapture = () => {
    if (mode === 'photo') {
      takePicture();
    } else {
      if (isRecording) {
        stopRecording();
      } else {
        startRecording();
      }
    }
  };

  if (hasPermission === null || hasMediaLibraryPermission === null) {
    return <View style={styles.center}><Text>Solicitando permisos...</Text></View>;
  }
  if (hasPermission === false) {
    return <View style={styles.center}><Text>Permiso de cámara denegado</Text></View>;
  }
  if (hasMediaLibraryPermission === false) {
    return <View style={styles.center}><Text>Permiso de galería denegado</Text></View>;
  }

  return (
    <View style={{ flex: 1 }}>
      <CameraPreview cameraRef={cameraRef} type={type} />
      <View style={styles.controls}>
        <ModeSwitcher mode={mode} onModeChange={setMode} />

        <CaptureButton
          onPress={handleCapture}
          isRecording={isCapturing || isRecording}
        />

        <View style={styles.bottomControls}>
          <TouchableOpacity onPress={() => setType(
            type === CameraType.back
              ? CameraType.front
              : CameraType.back
          )} style={styles.switchButton}>
            <Text style={{ color: '#fff' }}>Cambiar cámara</Text>
          </TouchableOpacity>

          <Button title="Configuración" onPress={() => navigation.navigate('SettingsScreen')} />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  controls: {
    position: 'absolute',
    bottom: 30,
    width: '100%',
    alignItems: 'center',
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
  },
  switchButton: {
    padding: 10,
    backgroundColor: '#222',
    borderRadius: 8,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
