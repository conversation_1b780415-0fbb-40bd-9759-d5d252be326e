import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import CaptureButton from './CaptureButton';

export default function BottomControls({ 
  onCapturePress, 
  onFlipCamera, 
  onMicToggle, 
  isRecording, 
  mode,
  isMicEnabled = true 
}) {
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.sideButton} onPress={onFlipCamera}>
        <Ionicons name="camera-reverse-outline" size={32} color="#fff" />
      </TouchableOpacity>
      
      <CaptureButton 
        onPress={onCapturePress} 
        isRecording={isRecording} 
        mode={mode}
      />
      
      <TouchableOpacity 
        style={[styles.sideButton, !isMicEnabled && styles.micDisabled]} 
        onPress={onMicToggle}
      >
        <Ionicons 
          name={isMicEnabled ? "mic-outline" : "mic-off-outline"} 
          size={32} 
          color={isMicEnabled ? "#fff" : "#FF3B30"} 
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
    paddingBottom: 80,
    paddingTop: 20,
  },
  sideButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.25,
    // shadowRadius: 3.84,
    // elevation: 5,
  },
  // micDisabled: {
  //   backgroundColor: 'rgba(255, 59, 48, 0.2)',
  // },
});
