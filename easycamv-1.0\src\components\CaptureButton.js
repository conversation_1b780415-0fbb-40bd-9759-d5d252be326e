import React from 'react';
import { TouchableOpacity, StyleSheet, View } from 'react-native';

export default function CaptureButton({ onPress, isRecording }) {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, isRecording && styles.recording]}
        onPress={onPress}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    margin: 20,
  },
  button: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#fff',
    borderWidth: 5,
    borderColor: '#e84118',
  },
  recording: {
    backgroundColor: '#e84118',
  },
});
