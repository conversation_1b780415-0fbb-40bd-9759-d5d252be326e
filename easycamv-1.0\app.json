{"expo": {"name": "EasyCam", "slug": "easycam-app", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Esta app necesita acceso a la cámara para tomar fotos y grabar videos.", "NSMicrophoneUsageDescription": "Esta app necesita acceso al micrófono para grabar videos con audio."}}, "android": {"permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}, "plugins": [["expo-camera", {"cameraPermission": "Permitir que $(PRODUCT_NAME) acceda a tu cámara", "microphonePermission": "Permitir que $(PRODUCT_NAME) acceda a tu micrófono", "recordAudioAndroid": true}]]}}