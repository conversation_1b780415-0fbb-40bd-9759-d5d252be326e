{"version": 3, "file": "MediaLibrary.js", "sourceRoot": "", "sources": ["../src/MediaLibrary.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,gBAAgB,EAGhB,oBAAoB,EACpB,mBAAmB,GAEpB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,YAAY,MAAM,oBAAoB,CAAC;AAE9C,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;AAEjF,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC,IAAI,QAAQ,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACrC,OAAO,CAAC,IAAI,CACV,wQAAwQ,CACzQ,CAAC;IACF,mBAAmB,GAAG,IAAI,CAAC;AAC7B,CAAC;AAqUD,OAAO,EACL,gBAAgB,GAKjB,CAAC;AAEF,SAAS,QAAQ,CAAI,IAAa;IAChC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5B,CAAC;AAED,SAAS,KAAK,CAAC,GAAyC;IACtD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IACD,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAClC,CAAC;AAED,SAAS,aAAa,CAAC,QAAmB;IACxC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,QAAmB;IACxC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,SAAkB;IACxC,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,SAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC9D,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;IACrD,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAAC,MAAe;IAClC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,MAAW;IACjC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,uBAAuB,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,MAA+B;IAC3D,WAAW,CAAC,MAAM,CAAC,CAAC;IACpB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACtD,CAAC;IACD,OAAO,GAAG,MAAM,OAAO,CAAC;AAC1B,CAAC;AAED,SAAS,YAAY,CAAC,KAAqB;IACzC,OAAO,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAoB,YAAY,CAAC,SAAS,CAAC;AAEjE,cAAc;AACd;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAiB,YAAY,CAAC,MAAM,CAAC;AAExD,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,OAAO,CAAC,CAAC,YAAY,IAAI,gBAAgB,IAAI,YAAY,CAAC;AAC5D,CAAC;AAED,2BAA2B;AAC3B;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,YAAqB,KAAK,EAC1B,mBAA0C;IAE1C,IAAI,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC;QAC1C,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,yBAAyB,CAAC,CAAC;IAC3E,CAAC;IACD,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;QAC9B,OAAO,MAAM,YAAY,CAAC,uBAAuB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;IACpF,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;AAC/D,CAAC;AAED,2BAA2B;AAC3B;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,YAAqB,KAAK,EAC1B,mBAA0C;IAE1C,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;QACtC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;IACvE,CAAC;IACD,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;QAC9B,OAAO,MAAM,YAAY,CAAC,mBAAmB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;IAChF,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAC3D,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,oBAAoB,CAGhD;IACA,4FAA4F;IAC5F,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,CAAC;IAC7F,aAAa,EAAE,CAAC,OAAO,EAAE,EAAE,CACzB,uBAAuB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,CAAC;CAC5E,CAAC,CAAC;AAEH,cAAc;AACd;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,KAAK,UAAU,6BAA6B,CACjD,aAAgC,CAAC,OAAO,EAAE,OAAO,CAAC;IAElD,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,QAAQ,EAAE,CAAC;QAC1C,MAAM,IAAI,mBAAmB,CAC3B,cAAc,EACd,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IACD,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QACxD,MAAM,YAAY,CAAC,uBAAuB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC9D,OAAO;IACT,CAAC;IACD,IAAI,CAAC,YAAY,CAAC,6BAA6B,EAAE,CAAC;QAChD,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;IACjF,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,6BAA6B,EAAE,CAAC;AAC5D,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,QAAgB,EAAE,KAAgB;IACvE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACnC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE7B,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC9C,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;IACvE,CAAC;IACD,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAErE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,sEAAsE;QACtE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CAAC,QAAgB;IACvD,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;QACrC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;IACtE,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,MAA6B,EAC7B,KAAe,EACf,OAAgB,IAAI;IAEpB,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;QACxC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;IACzE,CAAC;IAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE7B,aAAa,CAAC,QAAQ,CAAC,CAAC;IAExB,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,MAAM,YAAY,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7E,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,MAA6B,EAC7B,KAAe;IAEf,IAAI,CAAC,YAAY,CAAC,0BAA0B,EAAE,CAAC;QAC7C,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;IAC9E,CAAC;IAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE7B,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxB,OAAO,MAAM,YAAY,CAAC,0BAA0B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1E,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,MAA6B;IACnE,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAE7C,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxB,OAAO,MAAM,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACxD,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,KAAe,EACf,UAA6C,EAAE,yBAAyB,EAAE,IAAI,EAAE;IAEhF,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE7B,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAEzB,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAEzE,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,2EAA2E;QAC3E,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,EAAE,kBAAkB,GAAG,KAAK,KAAoB,EAAE;IAGrF,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QACjC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAClE,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,EAAE,kBAAkB,EAAE,CAAC,CAAC;AACnE,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,KAAa;IAC/C,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QAChC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACjD,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,SAAiB,EACjB,KAAgB,EAChB,YAAqB,IAAI,EACzB,oBAA6B;IAE7B,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACnC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE7B,IACE,QAAQ,CAAC,EAAE,KAAK,SAAS;QACzB,CAAC,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;QACrD,CAAC,oBAAoB,EACrB,CAAC;QACD,wFAAwF;QACxF,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;IACJ,CAAC;IACD,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAChD,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QACnD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,MAAM,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,oBAAoB,CAAC,CAAC;IACvF,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;AACpG,CAAC;AAED,cAAc;AACd;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,MAA6B,EAC7B,cAAuB,KAAK;IAE5B,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACrE,CAAC;IAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAE7C,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxB,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;QAC9B,OAAO,MAAM,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IACD,OAAO,MAAM,YAAY,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC;AACvE,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,gBAA+B,EAAE;IACpE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QACjC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAClE,CAAC;IAED,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,aAAa,CAAC;IAE9F,MAAM,OAAO,GAAG;QACd,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK;QACjC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;QACnB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;QACnB,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;QACxB,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnD,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC;QACxC,aAAa,EAAE,YAAY,CAAC,aAAa,CAAC;KAC3C,CAAC;IAEF,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IACD,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IACD,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IACD,IAAI,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAW,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;QAC9F,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACxD,CAAC;IACD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAChE,CAAC;IAED,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC1C,8DAA8D;IAC9D,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC;QACvC,GAAG,OAAO;QACV,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC;KACjD,CAAC,CAAC;AACL,CAAC;AAED,cAAc;AACd;;;;;;;;;;GAUG;AACH,MAAM,UAAU,WAAW,CACzB,QAAwD;IAExD,OAAO,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;AAC/E,CAAC;AAED,eAAe;AACf,MAAM,UAAU,kBAAkB,CAAC,YAA+B;IAChE,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,kBAAkB;IAChC,YAAY,CAAC,kBAAkB,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;AACrE,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAClC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;AAC9C,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAAC,KAAe;IAC7D,IAAI,CAAC,YAAY,CAAC,yBAAyB,EAAE,CAAC;QAC5C,OAAO;IACT,CAAC;IAED,OAAO,MAAM,YAAY,CAAC,yBAAyB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAAC,KAAe;IAC5D,IAAI,CAAC,YAAY,CAAC,wBAAwB,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,MAAM,YAAY,CAAC,wBAAwB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACnE,CAAC", "sourcesContent": ["import {\n  PermissionResponse as EXPermissionResponse,\n  PermissionStatus,\n  PermissionExpiration,\n  PermissionHookOptions,\n  createPermissionHook,\n  UnavailabilityError,\n  EventSubscription,\n} from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nimport MediaLibrary from './ExpoMediaLibrary';\n\nconst isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n\nlet loggedExpoGoWarning = false;\n\nif (isExpoGo && !loggedExpoGoWarning) {\n  console.warn(\n    'Due to changes in Androids permission requirements, Expo Go can no longer provide full access to the media library. To test the full functionality of this module, you can create a development build. https://docs.expo.dev/develop/development-builds/create-a-build'\n  );\n  loggedExpoGoWarning = true;\n}\n\n// @needsAudit\nexport type PermissionResponse = EXPermissionResponse & {\n  /**\n   * Indicates if your app has access to the whole or only part of the photo library. Possible values are:\n   * - `'all'` if the user granted your app access to the whole photo library\n   * - `'limited'` if the user granted your app access only to selected photos (only available on Android API 14+ and iOS 14.0+)\n   * - `'none'` if user denied or hasn't yet granted the permission\n   */\n  accessPrivileges?: 'all' | 'limited' | 'none';\n};\n\n/**\n * Determines the type of media that the app will ask the OS to get access to.\n * @platform android 13+\n */\nexport type GranularPermission = 'audio' | 'photo' | 'video';\n\nexport type MediaTypeValue = 'audio' | 'photo' | 'video' | 'unknown' | 'pairedVideo';\n\n/**\n * Represents the possible types of media that the app will ask the OS to get access to when calling [`presentPermissionsPickerAsync()`](#medialibrarypresentpermissionspickerasyncmediatypes).\n * @platform android 14+\n * */\nexport type MediaTypeFilter = 'photo' | 'video';\n\nexport type SortByKey =\n  | 'default'\n  | 'mediaType'\n  | 'width'\n  | 'height'\n  | 'creationTime'\n  | 'modificationTime'\n  | 'duration';\nexport type SortByValue = [SortByKey, boolean] | SortByKey;\n\ntype InternalSortByValue = `${SortByKey} ${'ASC' | 'DESC'}`;\n\nexport type MediaTypeObject = {\n  audio: 'audio';\n  photo: 'photo';\n  video: 'video';\n  unknown: 'unknown';\n};\n\nexport type SortByObject = {\n  default: 'default';\n  mediaType: 'mediaType';\n  width: 'width';\n  height: 'height';\n  creationTime: 'creationTime';\n  modificationTime: 'modificationTime';\n  duration: 'duration';\n};\n\n// @needsAudit\nexport type Asset = {\n  /**\n   * Internal ID that represents an asset.\n   */\n  id: string;\n  /**\n   * Filename of the asset.\n   */\n  filename: string;\n  /**\n   * URI that points to the asset. `ph://*` (iOS), `file://*` (Android)\n   */\n  uri: string;\n  /**\n   * Media type.\n   */\n  mediaType: MediaTypeValue;\n  /**\n   * An array of media subtypes.\n   * @platform ios\n   */\n  mediaSubtypes?: MediaSubtype[];\n  /**\n   * Width of the image or video.\n   */\n  width: number;\n  /**\n   * Height of the image or video.\n   */\n  height: number;\n  /**\n   * File creation timestamp.\n   */\n  creationTime: number;\n  /**\n   * Last modification timestamp.\n   */\n  modificationTime: number;\n  /**\n   * Duration of the video or audio asset in seconds.\n   */\n  duration: number;\n  /**\n   * Album ID that the asset belongs to.\n   * @platform android\n   */\n  albumId?: string;\n};\n\n// @needsAudit\nexport type AssetInfo = Asset & {\n  /**\n   * Local URI for the asset.\n   */\n  localUri?: string;\n  /**\n   * GPS location if available.\n   */\n  location?: Location;\n  /**\n   * EXIF metadata associated with the image.\n   */\n  exif?: object;\n  /**\n   * Whether the asset is marked as favorite.\n   * @platform ios\n   */\n  isFavorite?: boolean;\n  /**\n   * This field is available only if flag `shouldDownloadFromNetwork` is set to `false`.\n   * Whether the asset is stored on the network (iCloud on iOS).\n   * @platform ios\n   */\n  isNetworkAsset?: boolean; //iOS only\n  /**\n   * Display orientation of the image. Orientation is available only for assets whose\n   * `mediaType` is `MediaType.photo`. Value will range from 1 to 8, see [EXIF orientation specification](http://sylvana.net/jpegcrop/exif_orientation.html)\n   * for more details.\n   * @platform ios\n   */\n  orientation?: number;\n  /**\n   * Contains information about the video paired with the image file.\n   * This field is available if the `mediaType` is `\"photo\"`, and the `mediaSubtypes` includes `\"livePhoto\"`.\n   * @platform ios\n   */\n  pairedVideoAsset?: Asset | null;\n};\n\n/**\n * Constants identifying specific variations of asset media, such as panorama or screenshot photos,\n * and time-lapse or high-frame-rate video. Maps to [these values](https://developer.apple.com/documentation/photokit/phassetmediasubtype#1603888).\n * */\nexport type MediaSubtype =\n  | 'depthEffect'\n  | 'hdr'\n  | 'highFrameRate'\n  | 'livePhoto'\n  | 'panorama'\n  | 'screenshot'\n  | 'stream'\n  | 'timelapse';\n\n// @needsAudit\nexport type MediaLibraryAssetInfoQueryOptions = {\n  /**\n   * Whether allow the asset to be downloaded from network. Only available in iOS with iCloud assets.\n   * @default true\n   */\n  shouldDownloadFromNetwork?: boolean;\n};\n\n// @needsAudit\nexport type MediaLibraryAssetsChangeEvent = {\n  /**\n   * Whether the media library's changes could be described as \"incremental changes\".\n   * `true` indicates the changes are described by the `insertedAssets`, `deletedAssets` and\n   * `updatedAssets` values. `false` indicates that the scope of changes is too large and you\n   * should perform a full assets reload (eg. a user has changed access to individual assets in the\n   * media library).\n   */\n  hasIncrementalChanges: boolean;\n  /**\n   * Available only if `hasIncrementalChanges` is `true`.\n   * Array of [`Asset`](#asset)s that have been inserted to the library.\n   */\n  insertedAssets?: Asset[];\n  /**\n   * Available only if `hasIncrementalChanges` is `true`.\n   * Array of [`Asset`](#asset)s that have been deleted from the library.\n   */\n  deletedAssets?: Asset[];\n  /**\n   * Available only if `hasIncrementalChanges` is `true`.\n   * Array of [`Asset`](#asset)s that have been updated or completed downloading from network\n   * storage (iCloud on iOS).\n   */\n  updatedAssets?: Asset[];\n};\n\n// @docsMissing\nexport type Location = {\n  latitude: number;\n  longitude: number;\n};\n\n// @needsAudit\nexport type Album = {\n  /**\n   * Album ID.\n   */\n  id: string;\n  /**\n   * Album title.\n   */\n  title: string;\n  /**\n   * Estimated number of assets in the album.\n   */\n  assetCount: number;\n  /**\n   * The type of the assets album.\n   * @platform ios\n   */\n  type?: AlbumType;\n  /**\n   * Apply only to albums whose type is `'moment'`. Earliest creation timestamp of all\n   * assets in the moment.\n   * @platform ios\n   */\n  startTime: number;\n  /**\n   * Apply only to albums whose type is `'moment'`. Latest creation timestamp of all\n   * assets in the moment.\n   * @platform ios\n   */\n  endTime: number;\n  /**\n   * Apply only to albums whose type is `'moment'`. Approximated location of all\n   * assets in the moment.\n   * @platform ios\n   */\n  approximateLocation?: Location;\n  /**\n   * Apply only to albums whose type is `'moment'`. Names of locations grouped\n   * in the moment.\n   * @platform ios\n   */\n  locationNames?: string[];\n};\n\n// @docsMissing\nexport type AlbumType = 'album' | 'moment' | 'smartAlbum';\n\n// @docsMissing\nexport type AlbumsOptions = {\n  includeSmartAlbums?: boolean;\n};\n\n// @needsAudit\nexport type AssetsOptions = {\n  /**\n   * The maximum number of items on a single page.\n   * @default 20\n   */\n  first?: number;\n  /**\n   * Asset ID of the last item returned on the previous page. To get the ID of the next page,\n   * pass [`endCursor`](#pagedinfo) as its value.\n   */\n  after?: AssetRef;\n  /**\n   * [Album](#album) or its ID to get assets from specific album.\n   */\n  album?: AlbumRef;\n  /**\n   * An array of [`SortByValue`](#sortbyvalue)s or a single `SortByValue` value. By default, all\n   * keys are sorted in descending order, however you can also pass a pair `[key, ascending]` where\n   * the second item is a `boolean` value that means whether to use ascending order. Note that if\n   * the `SortBy.default` key is used, then `ascending` argument will not matter. Earlier items have\n   * higher priority when sorting out the results.\n   * If empty, this method uses the default sorting that is provided by the platform.\n   */\n  sortBy?: SortByValue[] | SortByValue;\n  /**\n   * An array of [MediaTypeValue](#mediatypevalue)s or a single `MediaTypeValue`.\n   * @default MediaType.photo\n   */\n  mediaType?: MediaTypeValue[] | MediaTypeValue;\n  /**\n   * `Date` object or Unix timestamp in milliseconds limiting returned assets only to those that\n   * were created after this date.\n   */\n  createdAfter?: Date | number;\n  /**\n   * Similarly as `createdAfter`, but limits assets only to those that were created before specified\n   * date.\n   */\n  createdBefore?: Date | number;\n};\n\n// @needsAudit\nexport type PagedInfo<T> = {\n  /**\n   * A page of [`Asset`](#asset)s fetched by the query.\n   */\n  assets: T[];\n  /**\n   * ID of the last fetched asset. It should be passed as `after` option in order to get the\n   * next page.\n   */\n  endCursor: string;\n  /**\n   * Whether there are more assets to fetch.\n   */\n  hasNextPage: boolean;\n  /**\n   * Estimated total number of assets that match the query.\n   */\n  totalCount: number;\n};\n\n// @docsMissing\nexport type AssetRef = Asset | string;\n\n// @docsMissing\nexport type AlbumRef = Album | string;\n\nexport {\n  PermissionStatus,\n  PermissionExpiration,\n  EXPermissionResponse,\n  PermissionHookOptions,\n  EventSubscription as Subscription,\n};\n\nfunction arrayize<T>(item: T | T[]): T[] {\n  if (Array.isArray(item)) {\n    return item;\n  }\n  return item ? [item] : [];\n}\n\nfunction getId(ref: string | undefined | { id?: string }): string | undefined {\n  if (typeof ref === 'string') {\n    return ref;\n  }\n  return ref ? ref.id : undefined;\n}\n\nfunction checkAssetIds(assetIds: unknown[]): asserts assetIds is string[] {\n  if (assetIds.some((id) => !id || typeof id !== 'string')) {\n    throw new Error('Asset ID must be a string!');\n  }\n}\n\nfunction checkAlbumIds(albumIds: unknown[]): asserts albumIds is string[] {\n  if (albumIds.some((id) => !id || typeof id !== 'string')) {\n    throw new Error('Album ID must be a string!');\n  }\n}\n\nfunction checkMediaType(mediaType: unknown): asserts mediaType is keyof MediaTypeObject {\n  if (Object.values(MediaType).indexOf(mediaType as any) === -1) {\n    throw new Error(`Invalid mediaType: ${mediaType}`);\n  }\n}\n\nfunction checkSortBy(sortBy: unknown): asserts sortBy is SortByValue {\n  if (Array.isArray(sortBy)) {\n    checkSortByKey(sortBy[0]);\n\n    if (typeof sortBy[1] !== 'boolean') {\n      throw new Error('Invalid sortBy array argument. Second item must be a boolean!');\n    }\n  } else {\n    checkSortByKey(sortBy);\n  }\n}\n\nfunction checkSortByKey(sortBy: any): void {\n  if (Object.values(SortBy).indexOf(sortBy) === -1) {\n    throw new Error(`Invalid sortBy key: ${sortBy}`);\n  }\n}\n\nfunction sortByOptionToString(sortBy: SortByValue | undefined): InternalSortByValue {\n  checkSortBy(sortBy);\n  if (Array.isArray(sortBy)) {\n    return `${sortBy[0]} ${sortBy[1] ? 'ASC' : 'DESC'}`;\n  }\n  return `${sortBy} DESC`;\n}\n\nfunction dateToNumber(value?: Date | number): number | undefined {\n  return value instanceof Date ? value.getTime() : value;\n}\n\n// @needsAudit\n/**\n * Possible media types.\n */\nexport const MediaType: MediaTypeObject = MediaLibrary.MediaType;\n\n// @needsAudit\n/**\n * Supported keys that can be used to sort `getAssetsAsync` results.\n */\nexport const SortBy: SortByObject = MediaLibrary.SortBy;\n\n// @needsAudit\n/**\n * Returns whether the Media Library API is enabled on the current device.\n * @return A promise which fulfils with a `boolean`, indicating whether the Media Library API is\n * available on the current device.\n */\nexport async function isAvailableAsync(): Promise<boolean> {\n  return !!MediaLibrary && 'getAssetsAsync' in MediaLibrary;\n}\n\n// @needsAudit @docsMissing\n/**\n * Asks the user to grant permissions for accessing media in user's media library.\n * @param writeOnly\n * @param granularPermissions - A list of [`GranularPermission`](#granularpermission) values. This parameter has an\n * effect only on Android 13 and newer. By default, `expo-media-library` will ask for all possible permissions.\n * @return A promise that fulfils with [`PermissionResponse`](#permissionresponse) object.\n */\nexport async function requestPermissionsAsync(\n  writeOnly: boolean = false,\n  granularPermissions?: GranularPermission[]\n): Promise<PermissionResponse> {\n  if (!MediaLibrary.requestPermissionsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'requestPermissionsAsync');\n  }\n  if (Platform.OS === 'android') {\n    return await MediaLibrary.requestPermissionsAsync(writeOnly, granularPermissions);\n  }\n  return await MediaLibrary.requestPermissionsAsync(writeOnly);\n}\n\n// @needsAudit @docsMissing\n/**\n * Checks user's permissions for accessing media library.\n * @param writeOnly\n * @param granularPermissions - A list of [`GranularPermission`](#granularpermission) values. This parameter has\n * an effect only on Android 13 and newer. By default, `expo-media-library` will ask for all possible permissions.\n * @return A promise that fulfils with [`PermissionResponse`](#permissionresponse) object.\n */\nexport async function getPermissionsAsync(\n  writeOnly: boolean = false,\n  granularPermissions?: GranularPermission[]\n): Promise<PermissionResponse> {\n  if (!MediaLibrary.getPermissionsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'getPermissionsAsync');\n  }\n  if (Platform.OS === 'android') {\n    return await MediaLibrary.getPermissionsAsync(writeOnly, granularPermissions);\n  }\n  return await MediaLibrary.getPermissionsAsync(writeOnly);\n}\n\n// @needsAudit\n/**\n * Check or request permissions to access the media library.\n * This uses both `requestPermissionsAsync` and `getPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [permissionResponse, requestPermission] = MediaLibrary.usePermissions();\n * ```\n */\nexport const usePermissions = createPermissionHook<\n  PermissionResponse,\n  { writeOnly?: boolean; granularPermissions?: GranularPermission[] }\n>({\n  // TODO(cedric): permission requesters should have an options param or a different requester\n  getMethod: (options) => getPermissionsAsync(options?.writeOnly, options?.granularPermissions),\n  requestMethod: (options) =>\n    requestPermissionsAsync(options?.writeOnly, options?.granularPermissions),\n});\n\n// @needsAudit\n/**\n * Allows the user to update the assets that your app has access to.\n * The system modal is only displayed if the user originally allowed only `limited` access to their\n * media library, otherwise this method is a no-op.\n * @param mediaTypes Limits the type(s) of media that the user will be granting access to. By default, a list that shows both photos and videos is presented.\n *\n * @return A promise that either rejects if the method is unavailable, or resolves to `void`.\n * > __Note:__ This method doesn't inform you if the user changes which assets your app has access to.\n * That information is only exposed by iOS, and to obtain it, you need to subscribe for updates to the user's media library using [`addListener()`](#medialibraryaddlistenerlistener).\n * If `hasIncrementalChanges` is `false`, the user changed their permissions.\n *\n * @platform android 14+\n * @platform ios\n */\nexport async function presentPermissionsPickerAsync(\n  mediaTypes: MediaTypeFilter[] = ['photo', 'video']\n): Promise<void> {\n  if (Platform.OS === 'android' && isExpoGo) {\n    throw new UnavailabilityError(\n      'MediaLibrary',\n      'presentPermissionsPickerAsync is unavailable in Expo Go'\n    );\n  }\n  if (Platform.OS === 'android' && Platform.Version >= 34) {\n    await MediaLibrary.requestPermissionsAsync(false, mediaTypes);\n    return;\n  }\n  if (!MediaLibrary.presentPermissionsPickerAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'presentPermissionsPickerAsync');\n  }\n  return await MediaLibrary.presentPermissionsPickerAsync();\n}\n\n// @needsAudit\n/**\n * Creates an asset from existing file. The most common use case is to save a picture taken by [Camera](./camera).\n * This method requires `CAMERA_ROLL` permission.\n *\n * @example\n * ```js\n * const { uri } = await Camera.takePictureAsync();\n * const asset = await MediaLibrary.createAssetAsync(uri);\n * ```\n * @param localUri A URI to the image or video file. It must contain an extension. On Android it\n * must be a local path, so it must start with `file:///`\n *\n * @param album An [Album](#album) or its ID. If provided, the asset will be added to this album upon creation, otherwise it will be added to the default album for the media type.\n * The album has exist.\n * @return A promise which fulfils with an object representing an [`Asset`](#asset).\n */\nexport async function createAssetAsync(localUri: string, album?: AlbumRef): Promise<Asset> {\n  if (!MediaLibrary.createAssetAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'createAssetAsync');\n  }\n\n  const albumId = getId(album);\n\n  if (!localUri || typeof localUri !== 'string') {\n    throw new Error('Invalid argument \"localUri\". It must be a string!');\n  }\n  const asset = await MediaLibrary.createAssetAsync(localUri, albumId);\n\n  if (Array.isArray(asset)) {\n    // Android returns an array with asset, we need to pick the first item\n    return asset[0];\n  }\n  return asset;\n}\n\n// @needsAudit\n/**\n * Saves the file at given `localUri` to the user's media library. Unlike [`createAssetAsync()`](#medialibrarycreateassetasynclocaluri),\n * This method doesn't return created asset.\n * On __iOS 11+__, it's possible to use this method without asking for `CAMERA_ROLL` permission,\n * however then yours `Info.plist` should have `NSPhotoLibraryAddUsageDescription` key.\n * @param localUri A URI to the image or video file. It must contain an extension. On Android it\n * must be a local path, so it must start with `file:///`.\n */\nexport async function saveToLibraryAsync(localUri: string): Promise<void> {\n  if (!MediaLibrary.saveToLibraryAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'saveToLibraryAsync');\n  }\n  return await MediaLibrary.saveToLibraryAsync(localUri);\n}\n\n// @needsAudit\n/**\n * Adds array of assets to the album.\n *\n * On Android, by default it copies assets from the current album to provided one, however it's also\n * possible to move them by passing `false` as `copyAssets` argument. In case they're copied you\n * should keep in mind that `getAssetsAsync` will return duplicated assets.\n * @param assets An array of [Asset](#asset) or their IDs.\n * @param album An [Album](#album) or its ID.\n * @param copy __Android only.__ Whether to copy assets to the new album instead of move them.\n * Defaults to `true`.\n * @return Returns promise which fulfils with `true` if the assets were successfully added to\n * the album.\n */\nexport async function addAssetsToAlbumAsync(\n  assets: AssetRef[] | AssetRef,\n  album: AlbumRef,\n  copy: boolean = true\n): Promise<boolean> {\n  if (!MediaLibrary.addAssetsToAlbumAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'addAssetsToAlbumAsync');\n  }\n\n  const assetIds = arrayize(assets).map(getId);\n  const albumId = getId(album);\n\n  checkAssetIds(assetIds);\n\n  if (!albumId || typeof albumId !== 'string') {\n    throw new Error('Invalid album ID. It must be a string!');\n  }\n\n  if (Platform.OS === 'ios') {\n    return await MediaLibrary.addAssetsToAlbumAsync(assetIds, albumId);\n  }\n  return await MediaLibrary.addAssetsToAlbumAsync(assetIds, albumId, !!copy);\n}\n\n// @needsAudit\n/**\n * Removes given assets from album.\n *\n * On Android, album will be automatically deleted if there are no more assets inside.\n * @param assets An array of [Asset](#asset) or their IDs.\n * @param album An [Album](#album) or its ID.\n * @return Returns promise which fulfils with `true` if the assets were successfully removed from\n * the album.\n */\nexport async function removeAssetsFromAlbumAsync(\n  assets: AssetRef[] | AssetRef,\n  album: AlbumRef\n): Promise<boolean> {\n  if (!MediaLibrary.removeAssetsFromAlbumAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'removeAssetsFromAlbumAsync');\n  }\n\n  const assetIds = arrayize(assets).map(getId);\n  const albumId = getId(album);\n\n  checkAssetIds(assetIds);\n  return await MediaLibrary.removeAssetsFromAlbumAsync(assetIds, albumId);\n}\n\n// @needsAudit\n/**\n * Deletes assets from the library. On iOS it deletes assets from all albums they belong to, while\n * on Android it keeps all copies of them (album is strictly connected to the asset). Also, there is\n * additional dialog on iOS that requires user to confirm this action.\n * @param assets An array of [Asset](#asset) or their IDs.\n * @return Returns promise which fulfils with `true` if the assets were successfully deleted.\n */\nexport async function deleteAssetsAsync(assets: AssetRef[] | AssetRef): Promise<boolean> {\n  if (!MediaLibrary.deleteAssetsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'deleteAssetsAsync');\n  }\n\n  const assetIds = arrayize(assets).map(getId);\n\n  checkAssetIds(assetIds);\n  return await MediaLibrary.deleteAssetsAsync(assetIds);\n}\n\n// @needsAudit\n/**\n * Provides more information about an asset, including GPS location, local URI and EXIF metadata.\n * @param asset An [Asset](#asset) or its ID.\n * @param options\n * @return An [AssetInfo](#assetinfo) object, which is an `Asset` extended by an additional fields.\n */\nexport async function getAssetInfoAsync(\n  asset: AssetRef,\n  options: MediaLibraryAssetInfoQueryOptions = { shouldDownloadFromNetwork: true }\n): Promise<AssetInfo> {\n  if (!MediaLibrary.getAssetInfoAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'getAssetInfoAsync');\n  }\n\n  const assetId = getId(asset);\n\n  checkAssetIds([assetId]);\n\n  const assetInfo = await MediaLibrary.getAssetInfoAsync(assetId, options);\n\n  if (Array.isArray(assetInfo)) {\n    // Android returns an array with asset info, we need to pick the first item\n    return assetInfo[0];\n  }\n  return assetInfo;\n}\n\n// @needsAudit\n/**\n * Queries for user-created albums in media gallery.\n * @return A promise which fulfils with an array of [`Album`](#asset)s. Depending on Android version,\n * root directory of your storage may be listed as album titled `\"0\"` or unlisted at all.\n */\nexport async function getAlbumsAsync({ includeSmartAlbums = false }: AlbumsOptions = {}): Promise<\n  Album[]\n> {\n  if (!MediaLibrary.getAlbumsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'getAlbumsAsync');\n  }\n  return await MediaLibrary.getAlbumsAsync({ includeSmartAlbums });\n}\n\n// @needsAudit\n/**\n * Queries for an album with a specific name.\n * @param title Name of the album to look for.\n * @return An object representing an [`Album`](#album), if album with given name exists, otherwise\n * returns `null`.\n */\nexport async function getAlbumAsync(title: string): Promise<Album> {\n  if (!MediaLibrary.getAlbumAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'getAlbumAsync');\n  }\n  if (typeof title !== 'string') {\n    throw new Error('Album title must be a string!');\n  }\n  return await MediaLibrary.getAlbumAsync(title);\n}\n\n// @needsAudit\n/**\n * Creates an album with given name and initial asset. The asset parameter is required on Android,\n * since it's not possible to create empty album on this platform. On Android, by default it copies\n * given asset from the current album to the new one, however it's also possible to move it by\n * passing `false` as `copyAsset` argument.\n * In case it's copied you should keep in mind that `getAssetsAsync` will return duplicated asset.\n * > On Android, it's not possible to create an empty album. You must provide an existing asset to copy or move into the album or an uri of a local file, which will be used to create an initial asset for the album.\n * @param albumName Name of the album to create.\n * @param asset An [Asset](#asset) or its ID. On Android you either need to provide an asset or a localUri.\n * @param initialAssetLocalUri A URI to the local media file, which will be used to create the initial asset inside the album. It must contain an extension. On Android it\n * must be a local path, so it must start with `file:///`. If the `asset` was provided, this parameter will be ignored.\n * @param copyAsset __Android Only.__ Whether to copy asset to the new album instead of move it. This parameter is ignored if `asset` was not provided.\n * Defaults to `true`.\n * @return Newly created [`Album`](#album).\n */\nexport async function createAlbumAsync(\n  albumName: string,\n  asset?: AssetRef,\n  copyAsset: boolean = true,\n  initialAssetLocalUri?: string\n): Promise<Album> {\n  if (!MediaLibrary.createAlbumAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'createAlbumAsync');\n  }\n\n  const assetId = getId(asset);\n\n  if (\n    Platform.OS === 'android' &&\n    (typeof assetId !== 'string' || assetId.length === 0) &&\n    !initialAssetLocalUri\n  ) {\n    // it's not possible to create empty album on Android, so initial asset must be provided\n    throw new Error(\n      'MediaLibrary.createAlbumAsync must be called with an asset or a localUri on Android.'\n    );\n  }\n  if (!albumName || typeof albumName !== 'string') {\n    throw new Error('Invalid argument \"albumName\". It must be a string!');\n  }\n  if (assetId != null && typeof assetId !== 'string') {\n    throw new Error('Asset ID must be a string!');\n  }\n\n  if (Platform.OS === 'ios') {\n    return await MediaLibrary.createAlbumAsync(albumName, assetId, initialAssetLocalUri);\n  }\n  return await MediaLibrary.createAlbumAsync(albumName, assetId, !!copyAsset, initialAssetLocalUri);\n}\n\n// @needsAudit\n/**\n * Deletes given albums from the library. On Android by default it deletes assets belonging to given\n * albums from the library. On iOS it doesn't delete these assets, however it's possible to do by\n * passing `true` as `deleteAssets`.\n * @param albums An array of [`Album`](#asset)s or their IDs.\n * @param assetRemove __iOS Only.__ Whether to also delete assets belonging to given albums.\n * Defaults to `false`.\n * @return Returns a promise which fulfils with `true` if the albums were successfully deleted from\n * the library.\n */\nexport async function deleteAlbumsAsync(\n  albums: AlbumRef[] | AlbumRef,\n  assetRemove: boolean = false\n): Promise<boolean> {\n  if (!MediaLibrary.deleteAlbumsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'deleteAlbumsAsync');\n  }\n\n  const albumIds = arrayize(albums).map(getId);\n\n  checkAlbumIds(albumIds);\n  if (Platform.OS === 'android') {\n    return await MediaLibrary.deleteAlbumsAsync(albumIds);\n  }\n  return await MediaLibrary.deleteAlbumsAsync(albumIds, !!assetRemove);\n}\n\n// @needsAudit\n/**\n * Fetches a page of assets matching the provided criteria.\n * @param assetsOptions\n * @return A promise that fulfils with to [`PagedInfo`](#pagedinfo) object with array of [`Asset`](#asset)s.\n */\nexport async function getAssetsAsync(assetsOptions: AssetsOptions = {}): Promise<PagedInfo<Asset>> {\n  if (!MediaLibrary.getAssetsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'getAssetsAsync');\n  }\n\n  const { first, after, album, sortBy, mediaType, createdAfter, createdBefore } = assetsOptions;\n\n  const options = {\n    first: first == null ? 20 : first,\n    after: getId(after),\n    album: getId(album),\n    sortBy: arrayize(sortBy),\n    mediaType: arrayize(mediaType || [MediaType.photo]),\n    createdAfter: dateToNumber(createdAfter),\n    createdBefore: dateToNumber(createdBefore),\n  };\n\n  if (first != null && typeof options.first !== 'number') {\n    throw new Error('Option \"first\" must be a number!');\n  }\n  if (after != null && typeof options.after !== 'string') {\n    throw new Error('Option \"after\" must be a string!');\n  }\n  if (album != null && typeof options.album !== 'string') {\n    throw new Error('Option \"album\" must be a string!');\n  }\n  if (after != null && Platform.OS === 'android' && isNaN(parseInt(getId(after) as string, 10))) {\n    throw new Error('Option \"after\" must be a valid ID!');\n  }\n  if (first != null && first < 0) {\n    throw new Error('Option \"first\" must be a positive integer!');\n  }\n\n  options.mediaType.forEach(checkMediaType);\n  // TODO(@kitten): Add expected native types for `MediaLibrary`\n  return await MediaLibrary.getAssetsAsync({\n    ...options,\n    sortBy: options.sortBy.map(sortByOptionToString),\n  });\n}\n\n// @needsAudit\n/**\n * Subscribes for updates in user's media library.\n * @param listener A callback that is fired when any assets have been inserted or deleted from the\n * library. On Android it's invoked with an empty object. On iOS, it's invoked with [`MediaLibraryAssetsChangeEvent`](#medialibraryassetschangeevent)\n * object.\n *\n * Additionally, only on iOS, the listener is also invoked when the user changes access to individual assets in the media library\n * using `presentPermissionsPickerAsync()`.\n * @return An [`Subscription`](#subscription) object that you can call `remove()` on when you would\n * like to unsubscribe the listener.\n */\nexport function addListener(\n  listener: (event: MediaLibraryAssetsChangeEvent) => void\n): EventSubscription {\n  return MediaLibrary.addListener(MediaLibrary.CHANGE_LISTENER_NAME, listener);\n}\n\n// @docsMissing\nexport function removeSubscription(subscription: EventSubscription): void {\n  subscription.remove();\n}\n\n// @needsAudit\n/**\n * Removes all listeners.\n */\nexport function removeAllListeners(): void {\n  MediaLibrary.removeAllListeners(MediaLibrary.CHANGE_LISTENER_NAME);\n}\n\n// @needsAudit\n/**\n * Fetches a list of moments, which is a group of assets taken around the same place\n * and time.\n * @return An array of [albums](#album) whose type is `moment`.\n * @platform ios\n */\nexport async function getMomentsAsync() {\n  if (!MediaLibrary.getMomentsAsync) {\n    throw new UnavailabilityError('MediaLibrary', 'getMomentsAsync');\n  }\n\n  return await MediaLibrary.getMomentsAsync();\n}\n\n// @needsAudit\n/**\n * Moves album content to the special media directories on **Android R** or **above** if needed.\n * Those new locations are in line with the Android `scoped storage` - so your application won't\n * lose write permission to those directories in the future.\n *\n * This method does nothing if:\n * - app is running on **iOS**, **web** or **Android below R**\n * - app has **write permission** to the album folder\n *\n * The migration is possible when the album contains only compatible files types.\n * For instance, movies and pictures are compatible with each other, but music and pictures are not.\n * If automatic migration isn't possible, the function rejects.\n * In that case, you can use methods from the `expo-file-system` to migrate all your files manually.\n *\n * # Why do you need to migrate files?\n * __Android R__ introduced a lot of changes in the storage system. Now applications can't save\n * anything to the root directory. The only available locations are from the `MediaStore` API.\n * Unfortunately, the media library stored albums in folders for which, because of those changes,\n * the application doesn't have permissions anymore. However, it doesn't mean you need to migrate\n * all your albums. If your application doesn't add assets to albums, you don't have to migrate.\n * Everything will work as it used to. You can read more about scoped storage in [the Android documentation](https://developer.android.com/about/versions/11/privacy/storage).\n *\n * @param album An [Album](#album) or its ID.\n * @return A promise which fulfils to `void`.\n */\nexport async function migrateAlbumIfNeededAsync(album: AlbumRef): Promise<void> {\n  if (!MediaLibrary.migrateAlbumIfNeededAsync) {\n    return;\n  }\n\n  return await MediaLibrary.migrateAlbumIfNeededAsync(getId(album));\n}\n\n// @needsAudit\n/**\n * Checks if the album should be migrated to a different location. In other words, it checks if the\n * application has the write permission to the album folder. If not, it returns `true`, otherwise `false`.\n * > Note: For **Android below R**, **web** or **iOS**, this function always returns `false`.\n * @param album An [Album](#album) or its ID.\n * @return Returns a promise which fulfils with `true` if the album should be migrated.\n */\nexport async function albumNeedsMigrationAsync(album: AlbumRef): Promise<boolean> {\n  if (!MediaLibrary.albumNeedsMigrationAsync) {\n    return false;\n  }\n\n  return await MediaLibrary.albumNeedsMigrationAsync(getId(album));\n}\n"]}