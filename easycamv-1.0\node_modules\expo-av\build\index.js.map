{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,CAAC;AACjB,OAAO,EAAE,OAAO,IAAI,KAAK,EAAE,MAAM,SAAS,CAAC;AAE3C,IAAI,wBAAwB,GAAG,KAAK,CAAC;AAErC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAC9B,OAAO,CAAC,IAAI,CACV,6JAA6J,CAC9J,CAAC;IACF,wBAAwB,GAAG,IAAI,CAAC;AAClC,CAAC;AAED,cAAc,YAAY,CAAC;AAC3B,cAAc,eAAe,CAAC;AAC9B,cAAc,eAAe,CAAC", "sourcesContent": ["import * as Audio from './Audio';\nexport { Audio };\nexport { default as Video } from './Video';\n\nlet loggedDeprecationWarning = false;\n\nif (!loggedDeprecationWarning) {\n  console.warn(\n    '[expo-av]: Expo AV has been deprecated and will be removed in SDK 54. Use the `expo-audio` and `expo-video` packages to replace the required functionality.'\n  );\n  loggedDeprecationWarning = true;\n}\n\nexport * from './AV.types';\nexport * from './Audio.types';\nexport * from './Video.types';\n"]}