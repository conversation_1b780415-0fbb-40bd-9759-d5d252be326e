import React from 'react';
import { View, FlatList, TouchableOpacity, Image, StyleSheet, Dimensions, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');
const ITEM_SIZE = (width - 60) / 3; // 3 columnas con espaciado

export default function MediaGrid({ assets, onAssetPress, onLoadMore, hasNextPage, loading }) {
  const renderAsset = ({ item, index }) => (
    <TouchableOpacity
      style={styles.assetContainer}
      onPress={() => onAssetPress(item, index)}
      activeOpacity={0.8}
    >
      <Image source={{ uri: item.uri }} style={styles.assetImage} />
      
      {/* Indicador de video */}
      {item.mediaType === 'video' && (
        <View style={styles.videoIndicator}>
          <Ionicons name="play-circle" size={24} color="#fff" />
          <Text style={styles.videoDuration}>
            {formatDuration(item.duration)}
          </Text>
        </View>
      )}
      
      {/* Indicador de Live Photo (iOS) */}
      {item.mediaSubtypes?.includes('livePhoto') && (
        <View style={styles.livePhotoIndicator}>
          <Ionicons name="radio-button-on" size={16} color="#fff" />
        </View>
      )}
    </TouchableOpacity>
  );

  const formatDuration = (seconds) => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderFooter = () => {
    if (!hasNextPage) return null;
    
    return (
      <View style={styles.loadingFooter}>
        <Text style={styles.loadingText}>
          {loading ? 'Cargando...' : 'Cargar más'}
        </Text>
      </View>
    );
  };

  return (
    <FlatList
      data={assets}
      renderItem={renderAsset}
      keyExtractor={(item) => item.id}
      numColumns={3}
      contentContainerStyle={styles.container}
      showsVerticalScrollIndicator={false}
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    paddingTop: 10,
  },
  assetContainer: {
    width: ITEM_SIZE,
    height: ITEM_SIZE,
    marginRight: 10,
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  assetImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  videoIndicator: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
  },
  videoDuration: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  livePhotoIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 4,
  },
  loadingFooter: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    color: '#666',
    fontSize: 14,
  },
});
