{"version": 3, "file": "Audio.js", "sourceRoot": "", "sources": ["../src/Audio.ts"], "names": [], "mappings": "AAAA,OAAO,EAAa,uBAAuB,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AACxF,OAAO,UAAU,MAAM,cAAc,CAAC;AAEtC,cAAc,mBAAmB,CAAC;AAClC,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,sBAAsB,EAAE,MAAM,MAAM,CAAC;AAE9C,MAAM,oBAAoB,GAAG,CAC3B,aAAiC,EACjC,gBAA2B,EAChB,EAAE;IACb,KAAK,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,GAAsB,CAAC;YACpC,aAAa,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAQ,CAAC;QACtD,CAAC;IACH,CAAC;IACD,OAAO,aAA0B,CAAC;AACpC,CAAC,CAAC;AAEF,MAAM,WAAW,GAAc;IAC7B,kBAAkB,EAAE,KAAK;IACzB,mBAAmB,EAAE,mBAAmB,CAAC,aAAa;IACtD,oBAAoB,EAAE,KAAK;IAC3B,uBAAuB,EAAE,KAAK;IAC9B,uBAAuB,EAAE,uBAAuB,CAAC,UAAU;IAC3D,iBAAiB,EAAE,IAAI;IACvB,0BAA0B,EAAE,KAAK;CAClC,CAAC;AAEF,IAAI,gBAAgB,GAAqB,IAAI,CAAC;AAE9C,SAAS,mBAAmB;IAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,WAA+B;IACrE,MAAM,IAAI,GAAG,oBAAoB,CAAC,WAAW,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAEtE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;QACnD,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC;QAC3D,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;IAC5E,CAAC;IACD,IACE,OAAO,IAAI,CAAC,kBAAkB,KAAK,SAAS;QAC5C,OAAO,IAAI,CAAC,oBAAoB,KAAK,SAAS;QAC9C,OAAO,IAAI,CAAC,uBAAuB,KAAK,SAAS;QACjD,OAAO,IAAI,CAAC,iBAAiB,KAAK,SAAS;QAC3C,OAAO,IAAI,CAAC,0BAA0B,KAAK,SAAS,EACpD,CAAC;QACD,MAAM,IAAI,KAAK,CACb,iJAAiJ,CAClJ,CAAC;IACJ,CAAC;IACD,gBAAgB,GAAG,IAAI,CAAC;IACxB,OAAO,MAAM,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC", "sourcesContent": ["import { AudioMode, InterruptionModeAndroid, InterruptionModeIOS } from './Audio.types';\nimport ExponentAV from './ExponentAV';\n\nexport * from './Audio/Recording';\nexport * from './Audio/Sound';\nexport { setIsEnabledAsync } from './Audio/AudioAvailability';\nexport { PitchCorrectionQuality } from './AV';\n\nconst _populateMissingKeys = (\n  userAudioMode: Partial<AudioMode>,\n  defaultAudioMode: AudioMode\n): AudioMode => {\n  for (const key in defaultAudioMode) {\n    if (!userAudioMode.hasOwnProperty(key)) {\n      const prop = key as keyof AudioMode;\n      userAudioMode[prop] = defaultAudioMode[prop] as any;\n    }\n  }\n  return userAudioMode as AudioMode;\n};\n\nconst defaultMode: AudioMode = {\n  allowsRecordingIOS: false,\n  interruptionModeIOS: InterruptionModeIOS.MixWithOthers,\n  playsInSilentModeIOS: false,\n  staysActiveInBackground: false,\n  interruptionModeAndroid: InterruptionModeAndroid.DuckOthers,\n  shouldDuckAndroid: true,\n  playThroughEarpieceAndroid: false,\n};\n\nlet currentAudioMode: AudioMode | null = null;\n\nfunction getCurrentAudioMode(): AudioMode {\n  if (!currentAudioMode) {\n    return defaultMode;\n  }\n  return currentAudioMode;\n}\n\n/**\n * We provide this API to customize the audio experience on iOS and Android.\n * @param partialMode\n * @return A `Promise` that will reject if the audio mode could not be enabled for the device.\n */\nexport async function setAudioModeAsync(partialMode: Partial<AudioMode>): Promise<void> {\n  const mode = _populateMissingKeys(partialMode, getCurrentAudioMode());\n\n  if (!InterruptionModeIOS[mode.interruptionModeIOS]) {\n    throw new Error(`\"interruptionModeIOS\" was set to an invalid value.`);\n  }\n  if (!InterruptionModeAndroid[mode.interruptionModeAndroid]) {\n    throw new Error(`\"interruptionModeAndroid\" was set to an invalid value.`);\n  }\n  if (\n    typeof mode.allowsRecordingIOS !== 'boolean' ||\n    typeof mode.playsInSilentModeIOS !== 'boolean' ||\n    typeof mode.staysActiveInBackground !== 'boolean' ||\n    typeof mode.shouldDuckAndroid !== 'boolean' ||\n    typeof mode.playThroughEarpieceAndroid !== 'boolean'\n  ) {\n    throw new Error(\n      '\"allowsRecordingIOS\", \"playsInSilentModeIOS\", \"playThroughEarpieceAndroid\", \"staysActiveInBackground\" and \"shouldDuckAndroid\" must be booleans.'\n    );\n  }\n  currentAudioMode = mode;\n  return await ExponentAV.setAudioMode(mode);\n}\n"]}