<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/sharing/">
    <img
      src="../../.github/resources/expo-sharing.svg"
      alt="expo-sharing"
      height="64" />
  </a>
</p>

Provides a way to share files directly with other compatible applications.

# API documentation

- [Documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/sharing/)
- [Documentation for the main branch](https://docs.expo.dev/versions/unversioned/sdk/sharing/)

# Installation in managed Expo projects

For [managed](https://docs.expo.dev/archive/managed-vs-bare/) Expo projects, please follow the installation instructions in the [API documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/sharing/).

# Installation in bare React Native projects

For bare React Native projects, you must ensure that you have [installed and configured the `expo` package](https://docs.expo.dev/bare/installing-expo-modules/) before continuing.

### Add the package to your npm dependencies

```
npx expo install expo-sharing
```

### Configure for Android

No additional set up necessary.

# Contributing

Contributions are very welcome! Please refer to guidelines described in the [contributing guide](https://github.com/expo/expo#contributing).
